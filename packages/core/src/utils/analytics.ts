/**
 * 埋点相关工具函数
 */

/**
 * 设置用户埋点标识
 * @param userId
 */
export const setUserAnalyticsId = (userId?: string | null) => {
  try {
    // 检查是否在浏览器环境且存在埋点SDK
    if (typeof window === 'undefined') {
      return
    }

    const collectEvent = window.collectEvent
    if (!collectEvent || typeof collectEvent !== 'function') {
      console.warn('埋点SDK未初始化或不可用')
      return
    }

    if (userId) {
      collectEvent('config', { user_unique_id: userId })
      console.log('已设置埋点用户标识:', userId)
      return
    }

    console.warn('用户ID为空，无法设置埋点用户标识')
  } catch (error) {
    console.error('设置埋点用户标识失败:', error)
  }
}

/**
 * 清除用户埋点标识（用于退出登录）
 */
export const clearUserAnalyticsId = () => {
  try {
    if (typeof window === 'undefined') {
      return
    }

    const collectEvent = window.collectEvent
    if (!collectEvent || typeof collectEvent !== 'function') {
      return
    }

    // 清除用户标识
    collectEvent('config', { user_unique_id: null })
    console.log('已清除埋点用户标识')
  } catch (error) {
    console.error('清除埋点用户标识失败:', error)
  }
}
