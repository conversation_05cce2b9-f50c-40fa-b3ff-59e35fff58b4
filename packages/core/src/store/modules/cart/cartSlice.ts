import { createEntityAdapter, createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit'
import Big from 'big.js'
import isEqual from 'lodash-es/isEqual'
import isUndefined from 'lodash-es/isUndefined'
import sortBy from 'lodash-es/sortBy'
import uniq from 'lodash-es/uniq'

import { ONLY_N_COIN } from '../../../constants'
import { GetCartQtyQuery } from '../../../graphql/generated/graphql'
import {
  validateProductSalableQty,
  validateProductStatus,
  validateProductStock,
} from '../../../utils/helper'
import { RootState } from '../../store'

export type TCart = NonNullable<NonNullable<GetCartQtyQuery['customer']>['shipping_cart']>
export type TCartProductItems = NonNullable<TCart['items']>
export type TCartProductItem = NonNullable<TCartProductItems[number]>

// 产品类型分类 - all
const PRODUCT_TYPE_ALL = {
  label: '全部',
  value: 'all',
}

export type TCartSliceState = {
  // 购物车中的产品类型分类
  productTypes: (typeof PRODUCT_TYPE_ALL)[]
  // 选中的产品类型分类
  activeProductTypeCode: string
  // 是否是编辑模式
  isEditMode: boolean
  // 编辑模式下，待删除的产品 uid。这个 RN 版本使用Set数据结构会报错，修改为使用数组
  deleteProductUIDs: string[]
}

/**
 * 创建购物车产品实体
 */
const cartProductsAdapter = createEntityAdapter<
  {
    // 扩展字段
    uid: string
    quantity: number
    isSelected: boolean
    // 原字段
    rawProduct: TCartProductItem
  },
  string
>({
  selectId: (item) => item.uid,
})

const initialState = cartProductsAdapter.getInitialState<TCartSliceState>({
  // 购物车中的产品类型分类
  productTypes: [PRODUCT_TYPE_ALL],
  // 选中的产品类型分类
  activeProductTypeCode: PRODUCT_TYPE_ALL.value,
  // 是否是编辑模式
  isEditMode: false,
  // 编辑模式下，待删除的产品 uid。这个 RN 版本使用Set数据结构会报错，修改为使用数组
  deleteProductUIDs: [],
})

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    /**
     * 保存购物车所有产品数据
     */
    setAllCartProducts: {
      reducer: cartProductsAdapter.setAll,
      prepare: (payload) => {
        if (Array.isArray(payload) && payload.length) {
          const formatProducts = payload.map((product) => {
            // 当前产品是否可用
            const isAvailable =
              product?.product &&
              validateProductStatus(product?.product, 1) &&
              validateProductStock(product.product, 'IN_STOCK') &&
              validateProductSalableQty(product.product)

            return {
              // 扩展字段
              uid: product.uid,
              quantity: product.quantity,
              isSelected: isAvailable ? !!product.is_selected : false,
              // 原字段
              rawProduct: product,
            }
          })

          return { payload: formatProducts }
        }

        return { payload: [] }
      },
    },
    /**
     * 更新购物车单条产品数据
     */
    updateCartProductOne: (state, action) => {
      const {
        payload: { uid },
      } = action
      if (uid) {
        const product = state.entities[uid]
        if (product) {
          cartProductsAdapter.updateOne(state, {
            id: uid,
            changes: {
              rawProduct: action.payload,
            },
          })
        }
      }
    },
    /**
     * 保存购物车中的产品类型分类
     */
    setCartProductTypes: (state, action) => {
      const { payload } = action
      if (payload) {
        // 保留默认的"全部"选项，然后添加新的选项，并去重
        const allOption = state.productTypes.find((item) => item.value === PRODUCT_TYPE_ALL.value)
        const existingValues = new Set(state.productTypes.map((item) => item.value))
        const newOptions = payload.filter(
          (item: { label: string; value: string }) => !existingValues.has(item.value),
        )

        state.productTypes = allOption ? [allOption, ...newOptions] : [...payload]
      }
    },
    /**
     * 保存选中的产品类型分类
     */
    setCartActiveProductType: (state, action) => {
      const { payload } = action
      if (payload) {
        state.activeProductTypeCode = payload
      }
    },
    /**
     * 选中产品集合
     */
    selectedCartProducts: (state, action: PayloadAction<string[]>) => {
      const { payload: uids } = action

      if (uids && Array.isArray(uids)) {
        const updates = uids
          .flatMap((uid) => {
            const product = state.entities[uid]
            if (product && !product.isSelected) {
              return { id: uid, changes: { isSelected: true } }
            }
            return [] // 跳过已选中的产品
          })
          .filter(Boolean)

        cartProductsAdapter.updateMany(state, updates)
      }
    },
    /**
     * 取消选中产品集合
     */
    deselectedCartProducts: (state, action) => {
      const { payload: uids } = action

      if (uids && Array.isArray(uids)) {
        const updates = uids
          .flatMap((uid) => {
            const product = state.entities[uid]

            if (product && product.isSelected) {
              return { id: uid, changes: { isSelected: false } }
            }
            return [] // 跳过未选中的产品
          })
          .filter(Boolean)

        cartProductsAdapter.updateMany(state, updates)
      }
    },
    /**
     * 更新购物车产品数量
     */
    updateCartProductQty: (state, action) => {
      const {
        payload: { uid, quantity },
      } = action

      if (uid && quantity) {
        const product = state.entities[uid]
        if (product) {
          cartProductsAdapter.updateOne(state, {
            id: uid,
            changes: { quantity },
          })
        }
      }
    },
    /**
     * 删除购物车产品集合
     */
    deleteCartProducts: (state, action) => {
      const { payload: uids } = action
      if (uids && Array.isArray(uids)) {
        cartProductsAdapter.removeMany(state, uids)
        // 清空待删除的产品 uid
        state.deleteProductUIDs = []
      }
    },
    /**
     * 更新购物车是否处于编辑模式
     */
    updateCartEditMode: (state, action) => {
      const { payload: isEditMode } = action
      if (!isUndefined(isEditMode)) {
        state.isEditMode = isEditMode
      }
    },
    /**
     * 添加需要删除产品的 uid
     */
    addCartDeleteProductUIDs: (state, action: PayloadAction<string[]>) => {
      const { payload: UIDs } = action
      if (UIDs && Array.isArray(UIDs)) {
        state.deleteProductUIDs = uniq([...state.deleteProductUIDs, ...UIDs])
      }
    },
    /**
     * 移除需要删除产品的 uid
     */
    deleteCartDeleteProductUIDs: (state, action) => {
      const { payload: UIDs } = action
      if (UIDs && Array.isArray(UIDs)) {
        const deleteProductUIDs = [...state.deleteProductUIDs]
        const result = deleteProductUIDs.filter((uid) => !UIDs.includes(uid))
        state.deleteProductUIDs = result
      }
    },
    /**
     * 重置待删除产品的 uid
     */
    resetCartDeleteProductUIDs: (state) => {
      state.deleteProductUIDs = []
    },
  },
})

const { actions } = cartSlice

export const {
  setAllCartProducts,
  setCartProductTypes,
  updateCartProductOne,
  setCartActiveProductType,
  selectedCartProducts,
  deselectedCartProducts,
  updateCartProductQty,
  deleteCartProducts,
  updateCartEditMode,
  addCartDeleteProductUIDs,
  deleteCartDeleteProductUIDs,
  resetCartDeleteProductUIDs,
} = actions

const selectSelf = (state: RootState) => state.cart

export const {
  // 获取购物车所有产品 uid
  selectIds: selectCartAllProductIds,
  // 获取购物车所有产品数据
  selectAll: selectCartAllProducts,
  // 获取购物车单个产品数据
  selectById: selectCartProductById,
  // 获取购物车产品总数
  selectTotal: selectCartAllProductsTotal,
} = cartProductsAdapter.getSelectors(selectSelf)

/**
 * 获取购物车中的产品类型分类
 */
export const cartProductTypesSelector = createSelector(selectSelf, (state) => state.productTypes)

/**
 * 获取选中的产品类型分类
 */
export const cartActiveProductTypeSelector = createSelector(
  selectSelf,
  (state) => state.activeProductTypeCode,
)

/**
 * 获取当前选中产品类型的产品
 */
export const cartProductsByActiveProductTypeSelector = createSelector(
  selectCartAllProducts,
  cartActiveProductTypeSelector,
  (products, activeProductType) => {
    return products.filter((product) => {
      if (activeProductType === PRODUCT_TYPE_ALL.value) {
        return true
      }

      return product.rawProduct.product.delivery_method === activeProductType
    })
  },
)

/**
 * 获取购物车可用的产品（排除下架、没库存）集合
 */
export const cartAllAvailableProductsSelector = createSelector(
  selectCartAllProducts,
  (allProducts) => {
    return allProducts.filter((product) => {
      return (
        validateProductStatus(product.rawProduct.product, 1) &&
        validateProductStock(product.rawProduct.product, 'IN_STOCK') &&
        validateProductSalableQty(product.rawProduct.product)
      )
    })
  },
)

/**
 * 获取购物车可用的产品（排除下架、没库存）的 uid 集合
 */
export const cartAllAvailableProductsUidSelector = createSelector(
  cartAllAvailableProductsSelector,
  (allAvailableProducts) => {
    return allAvailableProducts.map((product) => product.uid)
  },
)

/**
 * 获取购物车中选中的产品集合
 */
export const cartSelectedProductsSelector = createSelector(
  cartAllAvailableProductsSelector,
  (allAvailableProducts) => {
    return allAvailableProducts.filter((product) => product.isSelected)
  },
)

/**
 * 获取购物车选中的产品 uid 集合
 */
export const cartSelectedProductsUidSelector = createSelector(
  cartSelectedProductsSelector,
  (products) => {
    return products.map((product) => product.uid)
  },
)

/**
 * 获取当前购物车产品是否全部选中
 */
export const cartIsAllSelectedProductsSelector = createSelector(
  cartSelectedProductsSelector,
  cartAllAvailableProductsSelector,
  (selectedProducts, allAvailableProducts) => {
    if (selectedProducts.length === 0 && allAvailableProducts.length === 0) {
      return false
    }

    return selectedProducts.length === allAvailableProducts.length
  },
)

/**
 * 获取购物车中选中的产品集合，排除纯N币产品
 */
export const cartExcludeSelectedProductsPureNCoinSelector = createSelector(
  cartSelectedProductsSelector,
  (products) => {
    return products.filter((product) => product.rawProduct.product.paymeng_method !== ONLY_N_COIN)
  },
)

/**
 * 获取购物车中选中的产品是否没有包含存N币产品
 */
export const cartIsExcludeSelectedProductsPureNCoinSelector = createSelector(
  cartExcludeSelectedProductsPureNCoinSelector,
  (products) => {
    return products.length > 0
  },
)

/**
 * 计算购物车选中产品的总价
 */
export const cartSelectedProductsTotalPriceSelector = createSelector(
  cartExcludeSelectedProductsPureNCoinSelector,
  (products) => {
    const result = products.reduce((total, product) => {
      const productTotal = new Big(product.quantity).times(
        product.rawProduct.product?.price_range?.maximum_price?.final_price?.value || 0,
      )

      return total.plus(productTotal)
    }, new Big(0))

    return result.toNumber()
  },
)

/**
 * 获取购物车中选中的产品集合，包含纯N币产品
 */
export const cartSelectedProductsIncludePureNCoinSelector = createSelector(
  cartSelectedProductsSelector,
  (products) => {
    return products.filter((product) => product.rawProduct.product.paymeng_method === ONLY_N_COIN)
  },
)

/**
 * 获取购物车中选中的产品是否有包含存N币产品
 */
export const cartIsIncludeSelectedProductsPureNCoinSelector = createSelector(
  cartSelectedProductsIncludePureNCoinSelector,
  (products) => {
    return products.length > 0
  },
)

/**
 * 计算购物车选中产品的纯N币总价
 */
export const cartSelectedProductsPureNCoinTotalSelector = createSelector(
  cartSelectedProductsIncludePureNCoinSelector,
  (products) => {
    const result = products.reduce((total, product) => {
      const productTotal = new Big(product.quantity).times(
        product.rawProduct.product?.price_range?.maximum_price?.final_price?.value || 0,
      )

      return total.plus(productTotal)
    }, new Big(0))

    return result.toNumber()
  },
)

/**
 * 获取是否处于编辑模式
 */
export const cartIsEditModeSelector = createSelector(selectSelf, (state) => state.isEditMode)

/**
 * 获取编辑模式下，待删除的产品 uid
 */
export const cartDeleteProductUIDsSelector = createSelector(
  selectSelf,
  (state) => state.deleteProductUIDs,
)

/**
 * 获取购物车要删除的产品是否全部选中
 */
export const cartIsAllSelectedDeleteProducts = createSelector(
  selectCartAllProductIds,
  cartDeleteProductUIDsSelector,
  (allProductIds, deleteProductUIDs) => {
    return isEqual(sortBy(allProductIds), sortBy(deleteProductUIDs))
  },
)

export default cartSlice
