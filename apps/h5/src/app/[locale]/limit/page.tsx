'use client'
import { useCallback, useState } from 'react'
import { generateOSSUrl } from '@ninebot/core'
import { useCountDown } from 'ahooks'
import { Button } from 'antd-mobile'

const Page = () => {
  const [disabled, setDisabled] = useState(true)
  const [countdown] = useCountDown({
    leftTime: 20 * 1000,
    onEnd: () => {
      setDisabled(false)
    },
  })

  const handleBack = useCallback(() => {
    if (disabled) {
      return
    }

    window.location.href = '/'
  }, [disabled])

  return (
    <div
      className="flex h-screen flex-col items-center justify-center bg-cover"
      style={{
        backgroundImage: `url(${generateOSSUrl('/images/limit-bg.png')})`,
      }}>
      <div className="mx-[32px] flex flex-col items-center justify-center rounded-[16px] bg-white px-[20px] py-[24px]">
        <p className="mb-[24px] px-[24px] text-center font-miSansMedium380 font-[15px] leading-[20px] text-[#6E6E73]">
          当前访问人数较多，建议您等待倒计时结束后再次尝试访问
        </p>
        <Button className="nb-button w-full" color="primary" onClick={handleBack}>
          {disabled ? <>（{Math.ceil(countdown / 1000)} s）后尝试访问</> : '尝试访问'}
        </Button>
      </div>
    </div>
  )
}

export default Page
