import { createContext, useCallback, useContext, useEffect, useState } from 'react'
import {
  PRECISE_RATE_LIMIT,
  TCheckoutCartProductItem,
  TCheckoutCartProductItems,
  useGetCartQtyQuery,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { TRACK_EVENT } from '@ninebot/core/src/constants/global'
import {
  cartActiveProductTypeSelector,
  cartIsEditModeSelector,
  cartProductsByActiveProductTypeSelector,
  cartProductTypesSelector,
  resetCartDeleteProductUIDs,
  resetCheckout,
  selectCartAllProductsTotal,
  setAllCartProducts,
  setCartActiveProductType,
  setCartProductTypes,
  updateCartProductOne,
} from '@ninebot/core/src/store'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { formatProduct, formatProducts } from '@ninebot/core/src/utils/helper'

import type { CartProduct, ProductStatus } from '@/types/product'
interface ProductType {
  label: string
  value: string
}

interface CartContextValue {
  isEditMode: boolean
  cartState: ProductStatus
  setCartState: React.Dispatch<React.SetStateAction<ProductStatus>>
  showContent: boolean
  cartAllProductsTotal: number
  cartProductTypes: ProductType[]
  cartFilteredProducts: CartProduct[]
  handleProductTypeChange: (typeValue: string) => void
  cartActiveProductType: string
  handleRefresh: () => Promise<void>
  handleOptionsSwitch: (data: TCheckoutCartProductItem) => void
}

const CartContext = createContext<CartContextValue | null>(null)

export function CartProvider({ children }: { children: React.ReactNode }) {
  // 购物车状态
  const [cartState, setCartState] = useState<ProductStatus>({
    id: '',
    popVisible: false,
    sku: '',
    parentSku: '',
    quantity: 1,
    product: {} as CartProduct,
    servicePrice: 0,
  })

  const dispatch = useAppDispatch()
  const { reportEvent } = useVolcAnalytics()
  const toast = useToastContext()

  const cartFilteredProducts = useAppSelector(cartProductsByActiveProductTypeSelector)
  const cartProductTypes = useAppSelector(cartProductTypesSelector)
  const cartActiveProductType = useAppSelector(cartActiveProductTypeSelector)
  const cartAllProductsTotal = useAppSelector(selectCartAllProductsTotal)
  const isEditMode = useAppSelector(cartIsEditModeSelector)

  const [showContent, setShowContent] = useState(false)

  const {
    currentData,
    isFetching: cartQtyQueryLoading,
    refetch: refetchCartQtyQuery,
    error,
  } = useGetCartQtyQuery({
    currentPage: 1,
    pageSize: 10000,
  })

  /**
   * 骨架屏处理
   */
  useEffect(() => {
    let timeId: NodeJS.Timeout | null = null

    if (!cartQtyQueryLoading) {
      timeId = setTimeout(() => {
        setShowContent(true)
      }, 15)
    }

    return () => {
      if (timeId) clearTimeout(timeId)
      timeId = null
    }
  }, [cartQtyQueryLoading])

  /**
   * 每次进入页面更新数据
   */
  useEffect(() => {
    refetchCartQtyQuery()
    return () => {
      // 清理函数
    }
  }, [refetchCartQtyQuery])

  /**
   * 每次进入页面都清除 checkout 数据和待删除商品状态
   */
  useEffect(() => {
    dispatch(resetCheckout())
    dispatch(resetCartDeleteProductUIDs())
    return () => {
      // 清理函数
    }
  }, [dispatch])

  /**
   * 购物车产品数据，保存到 store
   */
  useEffect(() => {
    const cartItems = currentData?.customer?.shipping_cart?.items
    if (cartItems?.length) {
      dispatch(setAllCartProducts(formatProducts(cartItems as TCheckoutCartProductItems)))
    } else {
      dispatch(setAllCartProducts([]))
    }
  }, [currentData, dispatch])

  /**
   * 购物车产品类型分类数据，保存到 store
   */
  useEffect(() => {
    const cartTypes = currentData?.customAttributeMetadata?.items

    if (cartTypes?.[0]?.attribute_options && cartProductTypes.length === 1) {
      dispatch(setCartProductTypes(cartTypes[0].attribute_options))
    }
  }, [currentData, dispatch, cartProductTypes.length])

  /**
   * 埋点：点击购物车
   */
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_cart_page_exposure)
  }, [reportEvent])

  /**
   * 处理getCartQty接口限流错误
   */
  useEffect(() => {
    if (error) {
      const err = error as { type?: string; data?: string }

      // 检查是否为限流错误
      if (err?.type === PRECISE_RATE_LIMIT) {
        const errorMessage = err.data || '当前访问人数过多，请稍后再试！'
        toast.show({
          icon: 'fail',
          content: errorMessage,
        })
      }
    }
  }, [error, toast])

  /**
   * 修改选中的产品类型分类事件
   */
  const handleProductTypeChange = useCallback(
    (typeValue: string) => {
      dispatch(setCartActiveProductType(typeValue))
    },
    [dispatch],
  )

  /**
   * 下拉刷新，重新请求数据
   */
  const handleRefresh = useCallback(async () => {
    await refetchCartQtyQuery()
  }, [refetchCartQtyQuery])

  /**
   * 切换规格后的产品数据
   */
  const handleOptionsSwitch = (data: TCheckoutCartProductItem) => {
    const result = formatProduct(data)
    dispatch(updateCartProductOne(result))
  }

  const value: CartContextValue = {
    isEditMode,
    cartState,
    setCartState,
    showContent,
    cartAllProductsTotal,
    cartProductTypes,
    cartFilteredProducts,
    handleProductTypeChange,
    cartActiveProductType,
    handleRefresh,
    handleOptionsSwitch,
  }

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>
}

export function useCart() {
  const context = useContext(CartContext)
  if (!context) {
    throw new Error('useCart must be used within a CartProvider')
  }
  return context
}
