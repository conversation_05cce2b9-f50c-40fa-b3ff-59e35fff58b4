'use client'

import { useCallback, useEffect, useRef, useState } from 'react'

/**
 * 限流处理配置
 */
export type TRateLimitConfig = {
  originalText?: string // 按钮原始文案
}

/**
 * 限流处理结果
 */
export type TRateLimitResult = {
  isRateLimited: boolean
  countdown: number
  buttonText: string
  isDisabled: boolean
}

/**
 * 限流处理Hook
 */
export const useRateLimitHandler = (
  config: TRateLimitConfig,
): TRateLimitResult & {
  startRateLimit: (retryMs: number) => void
  reset: () => void
} => {
  const { originalText = '' } = config

  const [isRateLimited, setIsRateLimited] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  /**
   * 清理定时器
   */
  const clearTimer = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }, [])

  /**
   * 开始限流倒计时
   */
  const startRateLimit = useCallback(
    (retryMs: number) => {
      clearTimer() // 清理之前的定时器
      setIsRateLimited(true)
      setCountdown(Math.ceil(retryMs / 1000))

      intervalRef.current = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setIsRateLimited(false)
            clearInterval(intervalRef.current!)
            intervalRef.current = null
            return 0
          }
          return prev - 1
        })
      }, 1000)
    },
    [clearTimer],
  )

  /**
   * 重置状态
   */
  const reset = useCallback(() => {
    clearTimer()
    setIsRateLimited(false)
    setCountdown(0)
  }, [clearTimer])

  /**
   * 生成按钮文案
   */
  const buttonText = isRateLimited ? `${originalText}(${countdown}s)` : originalText

  /**
   * 组件卸载时清理定时器
   */
  useEffect(() => {
    return clearTimer
  }, [clearTimer])

  return {
    isRateLimited,
    countdown,
    buttonText,
    isDisabled: isRateLimited,
    startRateLimit,
    reset,
  }
}
export default useRateLimitHandler
