import { TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'

import { useCart } from '../context/cartContext'

export default function CartTabs() {
  const { cartProductTypes, handleProductTypeChange, cartActiveProductType } = useCart()
  const { reportEvent } = useVolcAnalytics()

  const handleTabClick = (value: string, label: string) => {
    // 埋点：购物车页分类tab点击
    reportEvent(TRACK_EVENT.shop_cart_tab_click, {
      category_id: value,
      category_name: label,
    })

    handleProductTypeChange(value)
  }

  return (
    <div className="mb-base-24 flex gap-base-12">
      {cartProductTypes.map((item, index) => (
        <button
          key={item.value + index}
          className={`h-[30px] rounded-full border border-gray-base px-base-16 py-base font-miSansDemiBold450 text-label-sm leading-none transition-colors ${
            cartActiveProductType === item.value
              ? 'border-primary bg-[#DA291C0D] text-primary'
              : 'bg-gray-base'
          }`}
          onClick={() => handleTabClick(item.value, item.label)}>
          {item.label}
        </button>
      ))}
    </div>
  )
}
