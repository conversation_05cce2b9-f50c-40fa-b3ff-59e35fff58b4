'use client'

import { TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'
import { Tabs } from 'antd-mobile'

import { useCart } from '../context/cartContext'

// 定义产品类型接口
interface ProductType {
  label: string
  value: string
}

export default function CartNavTabs() {
  const { cartProductTypes, handleProductTypeChange, cartActiveProductType } = useCart()
  const { reportEvent } = useVolcAnalytics()

  const handleTypeChange = (typeValue: string) => {
    const typeLabel = cartProductTypes.find((item) => item.value === typeValue)?.label

    // 埋点：购物车页分类tab点击
    reportEvent(TRACK_EVENT.shop_cart_tab_click, {
      category_id: typeValue,
      category_name: typeLabel || '',
    })

    handleProductTypeChange(typeValue)
  }

  return (
    <div className="sticky top-[56px] z-10 flex items-center justify-between bg-gray-base">
      {/* Tab切换 */}
      <Tabs
        activeKey={cartActiveProductType || cartProductTypes[0].value}
        onChange={(key) => handleTypeChange(key)}
        className={'custom-tabs cart-tabs flex-1'}
        activeLineMode="fixed">
        {cartProductTypes.map((item: ProductType, index) => (
          <Tabs.Tab
            title={item.label}
            key={item.value + index}
            className={'custom-tab-item cart-tab-item text-[#6E6E73]'}
          />
        ))}
      </Tabs>
    </div>
  )
}
